import { NextResponse } from 'next/server';

import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import { db } from '@/prisma/db';

import { generateDynamicAssessmentPrompt } from '@/prompt-service/strategies/ai-interview/dynamic-assessment-strategy';
import type { AssessmentCriteria, AssessmentTemplate } from '@/types/assessment';
import { Storage } from '@google-cloud/storage';
import { convertTailoredPracticeVideo } from '@/utils/video-conversion';

export const maxDuration = 299;

const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '';
const model = process.env.NEXT_PUBLIC_GCP_MODEL || '';
const location = process.env.NEXT_PUBLIC_GCP_LOCATION || '';
const defaultBucket = process.env.NEXT_PUBLIC_GCP_BUCKET || '';

// Helper function to get GCP bucket for organization
const getGCPBucket = async (organizationId: string) => {
  if (!organizationId) return defaultBucket;

  try {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { gcpBucket: true },
    });

    return organization?.gcpBucket || defaultBucket;
  } catch (error) {
    console.error('Error fetching organization GCP bucket:', error);
    return defaultBucket;
  }
};

export async function POST(_request: Request, { params }: { params: { id: string } }) {
  const { id } = params;

  try {
    // Get the AI interview (CareerPractice) with related data
    const aiInterview = await db.careerPractice.findUnique({
      where: { id },
      include: {
        user: {
          include: {
            userProfile: true,
          },
        },
        eventDetails: {
          include: {
            organization: true,
            assessmentTemplate: true,
          },
        },
      },
    });

    if (!aiInterview) {
      return NextResponse.json({ error: 'AI Interview not found' }, { status: 404 });
    }

    // Check if this is actually an AI interview
    if (!aiInterview.eventDetails?.isAiQuestion) {
      return NextResponse.json({ error: 'This is not an AI interview' }, { status: 400 });
    }

    // Check if feedback already exists
    if (aiInterview.feedback) {
      return NextResponse.json(
        {
          message: 'Feedback already generated',
          feedback: aiInterview.feedback,
        },
        { status: 200 },
      );
    }

    // Check if interview is completed
    if (aiInterview.interviewStatus !== 'COMPLETED') {
      return NextResponse.json({ error: 'Interview is not completed yet' }, { status: 400 });
    }

    // For AI interviews, the video is stored using the meeting ID (which is the same as the CareerPractice ID)
    // The video file is stored as: interview-ai/{meetingId}.m3u8
    const videoId = id; // Use the CareerPractice ID as the video identifier

    console.log('🎥 AI Interview Video Info:', {
      aiInterviewId: id,
      videoId: videoId,
      conversation: aiInterview.conversation,
      interviewStatus: aiInterview.interviewStatus,
    });

    console.log('AI Interview Feedback Generation:', {
      aiInterviewId: id,
      role: aiInterview.role,
      level: aiInterview.level,
      videoId: videoId,
      organizationId: aiInterview.eventDetails?.organizationId,
      expectedVideoPath: `interview-ai/${videoId}.m3u8`,
    });

    // Get bucket from organization - force use of 'aceprep' for AI interviews like HLS playlists
    const orgBucket = await getGCPBucket(aiInterview.eventDetails?.organizationId || '');
    const bucket = 'aceprep'; // Force use of aceprep bucket for AI interviews

    console.log('🪣 Bucket Resolution:', {
      organizationId: aiInterview.eventDetails?.organizationId,
      orgBucket: orgBucket,
      forcedBucket: bucket,
    });

    // Get additional context for better feedback
    const jobDescription = aiInterview.eventDetails?.jobDescription;
    const skillsAndKeywords = aiInterview.eventDetails?.skillsAndKeywords || [];

    // Get organization core values (if available)
    let coreValues = 'Standard professional values: integrity, teamwork, excellence, innovation, customer focus';
    if (aiInterview.eventDetails?.organizationId) {
      try {
        const organization = await db.organization.findUnique({
          where: { id: aiInterview.eventDetails.organizationId },
          select: { coreValues: true },
        });
        if (organization?.coreValues) {
          coreValues = organization.coreValues;
        }
      } catch (error) {
        console.log('Could not fetch organization core values, using defaults');
      }
    }

    // Get assessment criteria from event details
    let assessmentCriteria: AssessmentCriteria[] = [];
    let assessmentTemplate: AssessmentTemplate | undefined;

    if (aiInterview.eventDetails?.assessmentCriteria) {
      // Parse custom assessment criteria from JSON
      assessmentCriteria = aiInterview.eventDetails.assessmentCriteria as AssessmentCriteria[];
    } else if (aiInterview.eventDetails?.assessmentTemplate) {
      // Use template criteria
      assessmentTemplate = aiInterview.eventDetails.assessmentTemplate as AssessmentTemplate;
      assessmentCriteria = assessmentTemplate.criteria as AssessmentCriteria[];
    }

    // Generate dynamic system prompt based on assessment criteria
    const systemInput = generateDynamicAssessmentPrompt({
      role: aiInterview.role,
      level: aiInterview.level,
      candidateName: aiInterview.user?.userProfile?.fullName || aiInterview.user?.email || 'Candidate',
      jobDescription: jobDescription || '',
      requiredSkills: skillsAndKeywords.join(', '),
      coreValues: coreValues,
      assessmentCriteria: assessmentCriteria.length > 0 ? assessmentCriteria : undefined,
      assessmentTemplate: assessmentTemplate,
    });

    console.log({systemInput})

    // Generate feedback using video analysis
    const rawFeedback = await generateAIInterviewFeedback({
      systemInput,
      videoId,
      bucket,
      role: aiInterview.role,
      level: aiInterview.level,
    });

    // Parse the feedback from Vertex AI response
    let parsedFeedback: any;
    try {
      // Extract JSON from the Vertex AI response
      if (rawFeedback?.parts?.[0]?.text) {
        const textContent = rawFeedback.parts[0].text;
        // Extract JSON from markdown code block
        const jsonMatch = textContent.match(/```json\n([\s\S]*?)\n```/);
        if (jsonMatch) {
          parsedFeedback = JSON.parse(jsonMatch[1]);
          console.log('✅ Successfully parsed feedback JSON');
        } else {
          // Try to parse the entire text as JSON
          parsedFeedback = JSON.parse(textContent);
        }
      } else {
        throw new Error('Invalid response format from Vertex AI');
      }
    } catch (parseError) {
      console.error('❌ Error parsing feedback JSON:', parseError);
      console.log('Raw feedback:', JSON.stringify(rawFeedback, null, 2));
      throw new Error('Failed to parse AI feedback response');
    }

    // Update the AI interview with parsed feedback
    await db.careerPractice.update({
      where: { id },
      data: {
        feedback: parsedFeedback, // Save the parsed JSON object
        timing: {
          ...(aiInterview.timing as any),
          feedBackGenerateTime: new Date(),
        },
      },
    });

    return NextResponse.json(
      {
        message: 'AI Interview feedback generated successfully',
        feedback: parsedFeedback,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Error generating AI interview feedback:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate AI interview feedback',
        details: error.message,
      },
      { status: 500 },
    );
  }
}

const generateAIInterviewFeedback = async ({ systemInput, videoId, bucket, role, level }) => {
  // Use provided bucket or default
  const bucketName = bucket || defaultBucket;

  // AI interviews are stored as HLS with multiple .ts segments
  // Since Vertex AI doesn't support .m3u8, we'll try to use the first .ts segment
  // or look for a converted .mp4 file

  console.log('🎥 Generating AI Interview Feedback:', {
    bucketName,
    videoId,
    role,
    level,
  });

  // First, try to find a converted MP4 file
  let videoPath = `interview-ai/${videoId}.mp4`;
  let mimeType = 'video/mp4';

  try {
    // Check if converted MP4 exists - use same auth pattern as other endpoints
    const storage = new Storage({
      projectId: project,
      credentials: {
        private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
        client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
        client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
      },
    });

    console.log('🔍 Checking for video files:', {
      bucketName,
      videoId,
      mp4Path: videoPath,
    });

    const mp4File = storage.bucket(bucketName).file(videoPath);
    const [mp4Exists] = await mp4File.exists();

    console.log('📁 MP4 file exists:', mp4Exists);

    if (!mp4Exists) {
      console.log('🔄 MP4 file not found, looking for HLS segments...');

      // Look for HLS segments with the pattern: {videoId}_{segmentNumber}.ts
      const [files] = await storage.bucket(bucketName).getFiles({
        prefix: `interview-ai/${videoId}`,
      });

      console.log('📂 Found files:', files.map((f: any) => f.name));

      // Find .ts segment files with the correct naming pattern
      const tsFiles = files.filter((file: any) =>
        file.name.endsWith('.ts') && file.name.includes(`${videoId}_`)
      );

      if (tsFiles.length > 0) {
        // Sort by segment number
        tsFiles.sort((a: any, b: any) => {
          const aNum = parseInt(a.name.match(/_(\d+)\.ts$/)?.[1] || '0');
          const bNum = parseInt(b.name.match(/_(\d+)\.ts$/)?.[1] || '0');
          return aNum - bNum;
        });

        console.log('📹 Available HLS segments:', tsFiles.map((f: any) => f.name));

        // SMART SOLUTION: Analyze all segments for complete interview coverage
        if (tsFiles.length > 1) {
          console.log('🧠 Using multi-segment analysis for complete interview coverage');
          return await analyzeMultipleSegments({
            tsFiles,
            bucketName,
            systemInput,
            role,
            level,
            videoId
          });
        } else {
          // Single segment - use normal analysis
          videoPath = tsFiles[0].name;
          mimeType = 'video/mp4';
          console.log('📹 Using single HLS segment:', videoPath);
        }
      } else {
        // Let's also check for .m3u8 file to confirm the structure
        const m3u8File = files.find((file: any) => file.name.endsWith('.m3u8'));
        console.log('📋 M3U8 playlist found:', m3u8File?.name || 'None');

        console.log('� All found files:', files.map((f: any) => f.name));

        throw new Error(`No .ts video segments found for AI interview ${videoId}. Found ${files.length} files but no segments with pattern ${videoId}_*.ts`);
      }
    } else {
      console.log('📹 Using converted MP4:', videoPath);
    }
  } catch (error) {
    console.error('Error checking video files:', error);
    throw new Error(`Unable to find suitable video file for analysis: ${error.message}`);
  }

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.7,
      topP: 0.8,
    },
  };

  console.log('🤖 Sending to Vertex AI:', {
    mimeType,
    fileUri: `gs://${bucketName}/${videoPath}`,
    role,
    level,
  });

  const vertexAI = new GCPVertexAI(config);
  const stream = await vertexAI.generateContent({
    video: {
      fileData: {
        mimeType: mimeType,
        fileUri: `gs://${bucketName}/${videoPath}`,
      },
    },
    question: `Analyze this AI interview recording and provide comprehensive feedback for a ${role} position at ${level} level. Focus on technical skills, communication, problem-solving approach, and overall job fit.`,
  });

  return stream;
};

// Multi-segment analysis function for complete interview coverage
const analyzeMultipleSegments = async ({ tsFiles, bucketName, systemInput, role, level, videoId }) => {
  console.log(`🎬 Starting multi-segment analysis for ${tsFiles.length} segments`);

  const segmentPromises = tsFiles.map(async (file: any, index: number) => {
    const segmentNumber = index + 1;
    const totalSegments = tsFiles.length;

    console.log(`📹 Analyzing segment ${segmentNumber}/${totalSegments}: ${file.name}`);

    // Create segment-specific prompt
    const segmentPrompt = `${systemInput}

**SEGMENT ANALYSIS CONTEXT:**
This is segment ${segmentNumber} of ${totalSegments} from a complete AI interview.
Analyze this segment and provide insights that will be combined with other segments.
Focus on extracting:
1. Questions asked in this segment
2. Candidate responses and behavior
3. Key insights and observations
4. Any notable moments or red flags

Provide a focused analysis for this segment that can be combined with others for a complete assessment.`;

    const config: VertexAIConfiguration = {
      project,
      model,
      location,
      systemInput: segmentPrompt,
      credentials: {
        private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
        client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
        client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
      },
      generationConfig: {
        maxOutputTokens: 4096, // Smaller per segment
        temperature: 0.7,
        topP: 0.8,
      },
    };

    const vertexAI = new GCPVertexAI(config);
    const segmentResult = await vertexAI.generateContent({
      video: {
        fileData: {
          mimeType: 'video/mp4',
          fileUri: `gs://${bucketName}/${file.name}`,
        },
      },
      question: `Analyze segment ${segmentNumber}/${totalSegments} of this AI interview for a ${role} position at ${level} level. Extract questions, responses, and key insights from this segment.`,
    });

    return {
      segmentNumber,
      fileName: file.name,
      analysis: segmentResult,
    };
  });

  // Analyze all segments in parallel
  console.log('⚡ Running parallel analysis of all segments...');
  const segmentResults = await Promise.all(segmentPromises);

  console.log('🔄 Combining segment analyses into final assessment...');

  // Combine all segment results into final comprehensive feedback
  return await combineSegmentAnalyses({
    segmentResults,
    systemInput,
    role,
    level,
    totalSegments: tsFiles.length
  });
};

// Function to combine multiple segment analyses into final feedback
const combineSegmentAnalyses = async ({ segmentResults, systemInput, role, level, totalSegments }) => {
  // Extract text content from each segment analysis
  const segmentTexts = segmentResults.map((result: any) => {
    const text = result.analysis?.parts?.[0]?.text || 'No analysis available';
    return `**SEGMENT ${result.segmentNumber} (${result.fileName}):**\n${text}\n\n`;
  }).join('');

  // Create comprehensive combination prompt
  const combinationPrompt = `${systemInput}

**MULTI-SEGMENT COMBINATION TASK:**
You have analyzed ${totalSegments} segments of a complete AI interview. Below are the individual segment analyses.
Your task is to combine these insights into a single, comprehensive assessment.

**SEGMENT ANALYSES:**
${segmentTexts}

**COMBINATION INSTRUCTIONS:**
1. Extract and combine all questions asked across segments
3. Provide unified assessment scores based on complete interview
4. Ensure no information is lost from any segment
5. Create comprehensive feedback that reflects the entire interview

Provide the final assessment in the required JSON format with  unified scoring.`;

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput: combinationPrompt,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.7,
      topP: 0.8,
    },
  };


  console.log({combinationPrompt})
  const vertexAI = new GCPVertexAI(config);
  const finalResult = await vertexAI.sendMessage([
    {
      text: `Combine the ${totalSegments} segment analyses into a comprehensive AI interview assessment for a ${role} position at ${level} level. Provide unified feedback.`,
    },
  ]);

  console.log('✅ Multi-segment analysis completed successfully');
  return finalResult;
};
