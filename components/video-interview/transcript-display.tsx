'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@camped-ui/card';
import { Badge } from '@camped-ui/badge';

import { MessageSquare, User, Bot, Clock, AlertCircle } from 'lucide-react';
import { Skeleton } from '@camped-ui/skeleton';

interface TranscriptItem {
  id: string;
  type: string;
  role: 'user' | 'assistant';
  content: string[];
  interrupted: boolean;
}

interface TranscriptMetadata {
  room_name: string;
  candidate_name: string;
  role: string;
  level: string;
  interview_round: string;
  upload_timestamp: string;
  content_type: string;
}

interface TranscriptData {
  items: TranscriptItem[];
  metadata: TranscriptMetadata;
}

interface TranscriptDisplayProps {
  practiceId: string;
  organizationId?: string;
  className?: string;
}

export const TranscriptDisplay: React.FC<TranscriptDisplayProps> = ({
  practiceId,
  organizationId,
  className = '',
}) => {
  console.log('🔄 TranscriptDisplay component rendered with props:', { practiceId, organizationId, className });

  const [transcriptData, setTranscriptData] = useState<TranscriptData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTranscript = async () => {
      console.log('🎯 TranscriptDisplay: Starting fetch with:', { practiceId, organizationId });

      if (!practiceId) {
        console.warn('❌ Missing practiceId for transcript fetch');
        setError('Practice ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const url = `/api/session-transcript?practiceId=${encodeURIComponent(practiceId)}${
          organizationId ? `&organizationId=${encodeURIComponent(organizationId)}` : ''
        }`;

        console.log('📡 Making API call to:', url);
        const response = await fetch(url);
        console.log('📡 API response status:', response.status);

        if (!response.ok) {
          if (response.status === 404) {
            console.log('📄 No transcript file found (404)');
            throw new Error('Transcript not available for this interview');
          }
          throw new Error(`Failed to fetch transcript: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('✅ Transcript data received:', data);
        setTranscriptData(data);
      } catch (err) {
        console.error('❌ Error fetching transcript:', err);
        setError(err instanceof Error ? err.message : 'Failed to load transcript');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTranscript();
  }, [practiceId, organizationId]);

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return timestamp;
    }
  };

  const renderMessage = (item: TranscriptItem, index: number) => {
    const isUser = item.role === 'user';
    const content = Array.isArray(item.content) ? item.content.join(' ') : item.content;

    return (
      <div
        key={item.id || index}
        className={`flex gap-3 mb-4 ${isUser ? 'justify-end' : 'justify-start'}`}
      >
        {!isUser && (
          <div className="flex-shrink-0">
            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
              <Bot className="w-4 h-4 text-blue-600" />
            </div>
          </div>
        )}
        
        <div className={`max-w-[70%] ${isUser ? 'order-first' : ''}`}>
          <div
            className={`rounded-lg px-4 py-2 ${
              isUser
                ? 'bg-blue-600 text-white ml-auto'
                : 'bg-gray-100 text-gray-900'
            }`}
          >
            <p className="text-sm whitespace-pre-wrap">{content}</p>
            {item.interrupted && (
              <Badge variant="outline" className="mt-1 text-xs">
                Interrupted
              </Badge>
            )}
          </div>
        </div>

        {isUser && (
          <div className="flex-shrink-0">
            <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
              <User className="w-4 h-4 text-green-600" />
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
       
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex gap-3">
                <Skeleton className="w-8 h-8 rounded-full" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            Transcript Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!transcriptData || !transcriptData.items || transcriptData.items.length === 0) {
    return (
      <Card className={className}>
       
        <CardContent>
          <p className="text-sm text-gray-600">No transcript available for this interview.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
     
      <CardContent>
        <div className="h-96 overflow-y-scroll pr-4">
          <div className="space-y-1">
            {transcriptData.items.map((item, index) => renderMessage(item, index))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
