import { NextApiRequest, NextApiResponse } from 'next';
import { getSignedUrlForPath } from './gcp-bucket';

export const config = {
  api: {
    bodyParser: false, // Disable body parsing for consistency
  },
};

/**
 * API endpoint to fetch session transcript J<PERSON><PERSON> files
 * stored in the session-transcripts folder in GCP bucket
 * 
 * Usage: /api/session-transcript?practiceId=<practiceId>&organizationId=<organizationId>
 */
const handler = async (request: NextApiRequest, response: NextApiResponse) => {
  // Add CORS headers
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return response.status(200).end();
  }

  // Only allow GET requests
  if (request.method !== 'GET') {
    return response.status(405).json({ error: 'Method not allowed' });
  }

  const {
    practiceId,
    organizationId,
  } = request.query as {
    practiceId?: string;
    organizationId?: string;
  };

  // Validate required parameters
  if (!practiceId) {
    return response.status(400).json({ 
      error: 'Missing required parameter: practiceId' 
    });
  }

  try {
    console.log('Fetching session transcript for:', { practiceId, organizationId });

    // Generate signed URL for the transcript JSON file
    const filePath = `session-transcripts/${practiceId}.json`;
    const signedUrl = await getSignedUrlForPath(filePath, organizationId);

    console.log('Generated signed URL for transcript:', signedUrl.substring(0, 100) + '...');

    // Fetch the transcript content
    try {
      const transcriptResponse = await fetch(signedUrl);
      if (!transcriptResponse.ok) {
        if (transcriptResponse.status === 404) {
          return response.status(404).json({ 
            error: 'Transcript not found',
            practiceId,
            filePath 
          });
        }
        throw new Error(`Failed to fetch transcript: ${transcriptResponse.status}`);
      }

      const transcriptData = await transcriptResponse.json();
      console.log('Successfully fetched transcript data');

      // Return the transcript data
      response.setHeader('Content-Type', 'application/json');
      response.setHeader('Access-Control-Allow-Origin', '*');
      response.setHeader('Cache-Control', 'no-cache');
      return response.status(200).json(transcriptData);

    } catch (fetchError) {
      console.error('Error fetching transcript:', fetchError);

      if (fetchError.message.includes('404') || fetchError.message.includes('not found')) {
        return response.status(404).json({
          error: 'Transcript not found',
          practiceId,
          filePath: `session-transcripts/${practiceId}.json`,
          details: fetchError.message
        });
      }

      return response.status(500).json({
        error: 'Failed to fetch transcript',
        practiceId,
        filePath: `session-transcripts/${practiceId}.json`,
        details: fetchError.message
      });
    }

  } catch (error) {
    console.error('Error generating signed URL for transcript:', error);
    return response.status(500).json({
      error: 'Failed to generate signed URL for transcript',
      details: error.message
    });
  }
};

export default handler;
